import { Injectable, Logger, NotFoundException, ForbiddenException } from '@nestjs/common';
import { CaseNoteRepository } from '../repositories/case-note.repository';
import { CaseRepository } from '../repositories/case.repository';
import { CreateNoteDto } from '../dto/create-note.dto';
import { UpdateNoteDto } from '../dto/update-note.dto';
import { CaseAuditService } from './case-audit.service';
import { Request } from 'express';
import { CaseNote } from '@app/common/typeorm/entities';
import { PaginationService, PaginatedResponse } from './pagination.service';
import { CaseNoteFilterDto } from '../dto/case-note-filter.dto';
import { CaseAuditAction } from '@app/common/typeorm/entities/tenant/case-audit.entity';

@Injectable()
export class CaseNoteService {
    private readonly logger = new Logger(CaseNoteService.name);

    constructor(
        private readonly caseNoteRepository: CaseNoteRepository,
        private readonly caseRepository: CaseRepository,
        private readonly caseAuditService: CaseAuditService,
        private readonly paginationService: PaginationService
    ) {}

    /**
     * Creates a new note for a case
     */
    async createNote(
        caseId: string,
        createNoteDto: CreateNoteDto,
        createdBy: string,
        createdByName: string,
        request: Request
    ): Promise<CaseNote> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        // Create note
        const note = await this.caseNoteRepository.create({
            caseId,
            title: createNoteDto.title,
            content: createNoteDto.content,
            createdBy,
            createdByName,
            isPinned: createNoteDto.isPinned || false,
            isPrivate: createNoteDto.isPrivate || false
        });

        const savedNote = await this.caseNoteRepository.save(note);

        // Log note creation
        await this.caseAuditService.logNoteAdded(
            caseId,
            createdBy,
            createdByName,
            request,
            savedNote.id
        );

        return savedNote;
    }

    /**
     * Gets all notes for a case
     */
    async getCaseNotes(caseId: string, includePrivate: boolean = false): Promise<CaseNote[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        return this.caseNoteRepository.findByCaseId(caseId, includePrivate);
    }

    /**
     * Gets a specific note by ID
     */
    async getNoteById(noteId: string): Promise<CaseNote> {
        const note = await this.caseNoteRepository.findOne({
            where: { id: noteId }
        });

        if (!note) {
            throw new NotFoundException(`Note with ID ${noteId} not found`);
        }

        return note;
    }

    /**
     * Toggles the pin status of a note
     */
    async togglePinStatus(noteId: string, isPinned: boolean): Promise<void> {
        const note = await this.getNoteById(noteId);

        if (note.isPinned === isPinned) {
            return; // No change needed
        }

        await this.caseNoteRepository.togglePinStatus(noteId, isPinned);
    }

    /**
     * Gets all pinned notes for a case
     */
    async getPinnedNotes(caseId: string): Promise<CaseNote[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        return this.caseNoteRepository.findPinnedNotes(caseId);
    }

    /**
     * Gets the most recent notes for a case
     * @param caseId The case ID
     * @param limit The maximum number of notes to return
     * @returns The most recent notes
     */
    async getRecentNotes(caseId: string, limit: number = 3): Promise<CaseNote[]> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        return this.caseNoteRepository.findRecentNotes(caseId, limit);
    }

    /**
     * Gets case notes with pagination and filtering
     * @param caseId The case ID
     * @param filterDto The filter criteria
     * @returns Paginated response of case notes
     */
    async getCaseNotesWithPagination(
        caseId: string,
        filterDto: CaseNoteFilterDto
    ): Promise<PaginatedResponse<CaseNote>> {
        // Verify case exists
        const caseEntity = await this.caseRepository.findOne({
            where: { id: caseId }
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${caseId} not found`);
        }

        const [notes, total] = await this.caseNoteRepository.findWithFilters(caseId, filterDto);

        return this.paginationService.createPaginatedResponse(
            notes,
            total,
            filterDto.page || 1,
            filterDto.limit || 10
        );
    }

    /**
     * Updates a note
     */
    async updateNote(
        noteId: string,
        updateNoteDto: UpdateNoteDto,
        updatedBy: string,
        updatedByName: string,
        request: Request
    ): Promise<CaseNote> {
        // Get the existing note
        const existingNote = await this.getNoteById(noteId);

        // Get user from request to check if they're super admin
        const user = request['user'];
        const isSuperAdmin = user?.isSuperAdmin || false;

        // Check if user has permission to update this note
        // Only the creator or super admin can update notes
        if (existingNote.createdBy !== updatedBy && !isSuperAdmin) {
            this.logger.warn(
                `User ${updatedBy} attempted to update note ${noteId} created by ${existingNote.createdBy}`
            );
            throw new ForbiddenException('You can only update notes that you created');
        }

        // Prepare update data - only include fields that are provided
        const updateData: Partial<CaseNote> = {};

        if (updateNoteDto.title !== undefined) {
            updateData.title = updateNoteDto.title;
        }

        if (updateNoteDto.content !== undefined) {
            updateData.content = updateNoteDto.content;
        }

        if (updateNoteDto.isPinned !== undefined) {
            updateData.isPinned = updateNoteDto.isPinned;
        }

        if (updateNoteDto.isPrivate !== undefined) {
            updateData.isPrivate = updateNoteDto.isPrivate;
        }

        // If no fields to update, return existing note
        if (Object.keys(updateData).length === 0) {
            return existingNote;
        }

        // Update the note
        await this.caseNoteRepository.update(noteId, updateData);

        // Get the updated note
        const updatedNote = await this.getNoteById(noteId);

        // Log note update
        await this.caseAuditService.logAction(
            existingNote.caseId,
            CaseAuditAction.NOTE_UPDATED,
            updatedBy,
            updatedByName,
            this.getIpAddress(request),
            {
                noteId: noteId,
                changes: updateData
            }
        );

        return updatedNote;
    }

    /**
     * Deletes a note
     */
    async deleteNote(
        noteId: string,
        deletedBy: string,
        deletedByName: string,
        request: Request
    ): Promise<void> {
        // Get the existing note
        const existingNote = await this.getNoteById(noteId);

        // Get user from request to check if they're super admin
        const user = request['user'];
        const isSuperAdmin = user?.isSuperAdmin || false;

        // Check if user has permission to delete this note
        // Only the creator or super admin can delete notes
        if (existingNote.createdBy !== deletedBy && !isSuperAdmin) {
            this.logger.warn(
                `User ${deletedBy} attempted to delete note ${noteId} created by ${existingNote.createdBy}`
            );
            throw new ForbiddenException('You can only delete notes that you created');
        }

        // Delete the note
        await this.caseNoteRepository.remove(existingNote);

        // Log note deletion
        await this.caseAuditService.logAction(
            existingNote.caseId,
            CaseAuditAction.NOTE_DELETED,
            deletedBy,
            deletedByName,
            this.getIpAddress(request),
            {
                noteId: noteId,
                noteTitle: existingNote.title
            }
        );
    }

    /**
     * Extracts IP address from request
     */
    private getIpAddress(request: Request): string {
        return (
            (request.headers['x-forwarded-for'] as string)?.split(',')[0] ||
            request.connection?.remoteAddress ||
            request.socket?.remoteAddress ||
            '127.0.0.1'
        );
    }
}
