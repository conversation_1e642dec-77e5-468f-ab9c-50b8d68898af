import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { CaseRepository } from '../repositories/case.repository';
import { ClientRepository } from '../repositories/client.repository';
import { PropertyRepository } from '../repositories/property.repository';
import { CaseNumberGenerator } from '../utils/case-number-generator';
import { CreateCaseDto } from '../dto/create-case.dto';
import { UpdateCaseDto } from '../dto/update-case.dto';
import { Case, CasePriority, CaseType } from '@app/common/typeorm/entities/tenant/case.entity';
import { CaseFilterDto } from '../dto/case-filter.dto';
import { PaginatedResponse, PaginationService } from './pagination.service';
import { CaseAuditService } from './case-audit.service';
import { CaseNoteService } from './case-note.service';
import { CaseAttachmentService } from './case-attachment.service';
import { CaseAssignmentService } from './case-assignment.service';
import { CaseContactService } from './case-contact.service';
import { CaseEventService } from './case-event.service';
import { CaseRelationService } from './case-relation.service';
import { CaseNotificationService } from './case-notification.service';
import { CaseAuditAction } from '@app/common/typeorm/entities/tenant/case-audit.entity';
import { Request } from 'express';
import { CaseStatus } from '@app/common/enums/case-status.enum';
import { CacheService } from '@app/common/cache/cache.service';
import { TenantContextService } from '@app/common/multi-tenancy/tenant-context.service';
import { CACHE_KEYS } from '@app/common/cache/cache.config';
import { ConfigService } from '@nestjs/config';
import { instance as logger } from '@app/common/utils';
import { ConveyancingMilestoneService } from './conveyancing-milestone.service';
import { CasePaymentService } from './case-payment.service';
import { validate as isUuid } from 'uuid';
import { TenantConnectionService } from '@app/common/multi-tenancy/tenant-connection.service';
import { Quote } from '@app/common/typeorm/entities/tenant/quote.entity';
import { RateCardRepository } from 'apps/quote-engine/src/repositories/rate-card.repository';
import { RateCardFeeItemRepository } from 'apps/quote-engine/src/repositories/rate-card-fee-item.repository';
import { RateCardFeeItem } from '@app/common/typeorm/entities/tenant/rate-card-fee-item.entity';
@Injectable()
export class CaseService {
    constructor(
        private readonly caseRepository: CaseRepository,
        private readonly clientRepository: ClientRepository,
        private readonly propertyRepository: PropertyRepository,
        private readonly caseNumberGenerator: CaseNumberGenerator,
        private readonly paginationService: PaginationService,
        private readonly caseAuditService: CaseAuditService,
        private readonly caseNoteService: CaseNoteService,
        private readonly caseAttachmentService: CaseAttachmentService,
        private readonly caseAssignmentService: CaseAssignmentService,
        private readonly caseContactService: CaseContactService,
        private readonly caseEventService: CaseEventService,
        private readonly caseRelationService: CaseRelationService,
        private readonly caseNotificationService: CaseNotificationService,
        private readonly cacheService: CacheService,
        private readonly tenantContext: TenantContextService,
        private readonly configService: ConfigService,
        private readonly conveyancingMilestoneService: ConveyancingMilestoneService,
        private readonly casePaymentService: CasePaymentService,
        private readonly rateCardRepository: RateCardRepository,
        private readonly rateCardFeeItemRepository: RateCardFeeItemRepository,
        private readonly tenantConnectionService: TenantConnectionService
    ) {
        logger.info('CaseService initialized');
    }

    /**
     * Find the Arrow Conveyancing rate card
     */
    private async getArrowConveyancingRateCard(): Promise<string | null> {
        try {
            const rateCard = await this.rateCardRepository.findByProvider('Arrow Conveyancing');
            return rateCard ? rateCard.id : null;
        } catch (error) {
            logger.warn(`Failed to find Arrow Conveyancing rate card: ${error.message}`);
            return null;
        }
    }

    /**
     * Creates a new case
     */
    async createCase(
        createCaseDto: CreateCaseDto,
        userId: string,
        userName: string,
        request: Request
    ): Promise<Case> {
        let clientId: string;

        // Handle client creation or verification
        if (createCaseDto.client) {
            // Create a new client
            const newClient = await this.clientRepository.create({
                ...createCaseDto.client,
                createdBy: userId
            });

            const savedClient = await this.clientRepository.save(newClient);
            clientId = savedClient.id;

            // Note: Client creation audit will be logged as part of case creation
        } else if (createCaseDto.clientId) {
            // Verify client exists
            const client = await this.clientRepository.findOne({
                where: { id: createCaseDto.clientId }
            });

            if (!client) {
                throw new BadRequestException(`Client with ID ${createCaseDto.clientId} not found`);
            }

            clientId = createCaseDto.clientId;
        } else {
            throw new BadRequestException('Either client or clientId must be provided');
        }

        // Handle property creation or verification
        let propertyId: string | null = null;
        if (createCaseDto.property) {
            // Create a new property
            const newProperty = await this.propertyRepository.create({
                ...createCaseDto.property,
                createdBy: userId,
                lastModifiedBy: userId
            });

            const savedProperty = await this.propertyRepository.save(newProperty);
            propertyId = savedProperty.id;

            // Note: Property creation audit will be logged as part of case creation
        } else if (createCaseDto.propertyId) {
            // Verify property exists
            const property = await this.propertyRepository.findOne({
                where: { id: createCaseDto.propertyId }
            });

            if (!property) {
                throw new BadRequestException(
                    `Property with ID ${createCaseDto.propertyId} not found`
                );
            }

            propertyId = createCaseDto.propertyId;
        }

        // Generate case number
        const caseNumber = await this.caseNumberGenerator.generateCaseNumber();

        // Get Arrow Conveyancing rate card ID
        const rateCardId = await this.getArrowConveyancingRateCard();

        // Create case
        const newCase = await this.caseRepository.create({
            caseNumber,
            title: createCaseDto.title,
            description: createCaseDto.description,
            priority: createCaseDto.priority || CasePriority.MEDIUM,
            type: createCaseDto.type || CaseType.OTHER,
            status: createCaseDto.status || CaseStatus.DRAFT,
            clientId,
            propertyId: propertyId || undefined,
            rateCardId: rateCardId || undefined,
            deadline: createCaseDto.deadline ? new Date(createCaseDto.deadline) : null,
            createdBy: userId,
            conveyancingMetadata: {
                ...(createCaseDto.transactionType && {
                    transactionType: createCaseDto.transactionType
                })
            }
        });

        const savedCase = await this.caseRepository.save(newCase);

        // Log case creation with specific status and related entities
        const auditDetails: any = {
            title: savedCase.title,
            clientId: savedCase.clientId,
            status: savedCase.status,
            caseNumber: savedCase.caseNumber
        };

        // Include property information if created
        if (propertyId && createCaseDto.property) {
            auditDetails.propertyId = propertyId;
            auditDetails.propertyAddress = createCaseDto.property.fullAddress;
        }

        // Include client information if created
        if (createCaseDto.client) {
            auditDetails.clientName = createCaseDto.client.name;
            auditDetails.clientEmail = createCaseDto.client.email;
        }

        await this.caseAuditService.logCaseCreation(
            savedCase.id,
            userId,
            userName,
            request,
            auditDetails
        );

        // Also log the specific status creation
        const statusAction = this.getStatusAction(savedCase.status);
        if (statusAction) {
            await this.caseAuditService.logAction(
                savedCase.id,
                statusAction,
                userId,
                userName,
                this.getIpAddress(request),
                { status: savedCase.status }
            );
        }

        // Send case creation notification
        const client = await this.clientRepository.findOne({ where: { id: clientId } });
        if (client && client.email) {
            await this.caseNotificationService.sendCaseCreatedNotification(
                { ...savedCase, client },
                userId,
                [client.email]
            );
        }

        // Add initial note if provided
        if (createCaseDto.initialNote) {
            await this.caseNoteService.createNote(
                savedCase.id,
                createCaseDto.initialNote,
                userId,
                userName,
                request
            );
        }

        // Add multiple notes if provided
        if (createCaseDto.notes && createCaseDto.notes.length > 0) {
            for (const note of createCaseDto.notes) {
                await this.caseNoteService.createNote(
                    savedCase.id,
                    note,
                    userId,
                    userName,
                    request
                );
            }
        }

        // Add initial attachment if provided
        if (createCaseDto.initialAttachment) {
            await this.caseAttachmentService.createAttachment(
                savedCase.id,
                createCaseDto.initialAttachment,
                userId,
                userName,
                request
            );
        }

        // Add multiple attachments if provided
        if (createCaseDto.attachments && createCaseDto.attachments.length > 0) {
            for (const attachment of createCaseDto.attachments) {
                await this.caseAttachmentService.createAttachment(
                    savedCase.id,
                    attachment,
                    userId,
                    userName,
                    request
                );
            }
        }

        // Invalidate cache
        const tenantId = this.tenantContext.getTenantId();
        const ttl = this.configService.get<number>('cache.ttl.caseDetails');
        try {
            await this.cacheService.set(
                CACHE_KEYS.CASE_BY_ID(tenantId, savedCase.id, userId),
                savedCase,
                ttl
            );
            logger.debug(
                `[CACHE SET] Case by ID (create): key=${CACHE_KEYS.CASE_BY_ID(tenantId, savedCase.id, userId)} | value: ${JSON.stringify(savedCase)}`
            );
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][CaseCreate] tenantId=${tenantId} userId=${userId} caseId=${savedCase.id} cacheKey=${CACHE_KEYS.CASE_BY_ID(tenantId, savedCase.id, userId)} - Failed to set case in cache: ${err?.message}`,
                err
            );
        }
        try {
            await this.cacheService.set(
                CACHE_KEYS.CASE_BY_NUMBER(tenantId, savedCase.caseNumber, userId),
                savedCase,
                ttl
            );
            logger.debug(
                `[CACHE SET] Case by Number (create): key=${CACHE_KEYS.CASE_BY_NUMBER(tenantId, savedCase.caseNumber, userId)} | value: ${JSON.stringify(savedCase)}`
            );
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][CaseCreate] tenantId=${tenantId} userId=${userId} caseNumber=${savedCase.caseNumber} cacheKey=${CACHE_KEYS.CASE_BY_NUMBER(tenantId, savedCase.caseNumber, userId)} - Failed to set case in cache: ${err?.message}`,
                err
            );
        }

        // Create case-specific milestones and tasks from seeded templates
        if (savedCase.type === CaseType.CONVEYANCING) {
            try {
                await this.conveyancingMilestoneService.createDefaultMilestonesForCase(
                    savedCase.id,
                    userId
                );
                logger.info(`Created default milestones for case: ${savedCase.id}`);
            } catch (error) {
                logger.error(`Failed to create milestones for case ${savedCase.id}:`, error);
                // Don't fail case creation if milestone creation fails
            }
        }

        // Return the case with all relations including property and rate card
        return await this.findCaseById(savedCase.id, userId, true);
    }

    /**
     * Finds a case by ID
     */
    async findCaseById(
        id: string,
        userId: string,
        includeRelations: boolean = true
    ): Promise<Case> {
        const tenantId = this.tenantContext.getTenantId();
        const cacheKey = CACHE_KEYS.CASE_BY_ID(tenantId, id, userId);
        let cachedCase: Case | undefined;
        try {
            cachedCase = await this.cacheService.get<Case>(cacheKey);
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][CaseById] tenantId=${tenantId} userId=${userId} caseId=${id} cacheKey=${cacheKey} - Failed to get case from cache: ${err?.message}`,
                err
            );
        }
        if (cachedCase) {
            logger.debug(
                `[CACHE HIT] Case by ID: key=${cacheKey} | value: ${JSON.stringify(cachedCase)}`
            );
            return cachedCase;
        }

        const relations = includeRelations ? ['client'] : [];
        const caseEntity = await this.caseRepository.findOne({
            where: { id },
            relations
        });

        if (!caseEntity) {
            throw new NotFoundException(`Case with ID ${id} not found`);
        }

        const ttl = this.configService.get<number>('cache.ttl.caseDetails');
        try {
            await this.cacheService.set(cacheKey, caseEntity, ttl);
            logger.debug(
                `[CACHE SET] Case by ID: key=${cacheKey} | value: ${JSON.stringify(caseEntity)}`
            );
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][CaseById] tenantId=${tenantId} userId=${userId} caseId=${id} cacheKey=${cacheKey} - Failed to set case in cache: ${err?.message}`,
                err
            );
        }
        return caseEntity;
    }

    /**
     * Gets comprehensive case details including all related information
     */
    async getCaseDetails(id: string, userId: string): Promise<any> {
        // Get the case with client information
        const caseEntity = await this.findCaseById(id, userId);

        // Get assignments
        const assignments = await this.caseAssignmentService.getCaseAssignments(id);

        // Get notes
        const notes = await this.caseNoteService.getCaseNotes(id, true);

        // Get attachments
        const attachments = await this.caseAttachmentService.getCaseAttachments(id);

        // Get contacts
        const contacts = await this.caseContactService.getCaseContacts(id);

        // Get related cases
        const relatedCases = await this.caseRelationService.getCaseRelations(id);

        // Get events (timeline)
        const events = await this.caseEventService.getCaseEvents(id);

        // Get audit trail
        const auditTrail = await this.caseAuditService.getCaseAuditTrail(id);

        // Get milestones with progress tracking (for conveyancing cases)
        let milestones: any[] = [];
        if (caseEntity.type === CaseType.CONVEYANCING || caseEntity.type === CaseType.REAL_ESTATE) {
            milestones = await this.conveyancingMilestoneService.getMilestonesWithProgress(id);
        }

        // Get payment history
        const payments = await this.casePaymentService.getCasePayments(id);

        // Get quote information (if case was created from a quote)
        let quote: any = null;
        try {
            const tenantId = this.tenantContext.getTenantId();
            const dataSource = await this.tenantConnectionService.getTenantDataSource(tenantId);
            const quoteRepository = dataSource.getRepository(Quote);
            quote = await quoteRepository.findOne({ where: { caseId: id } });
        } catch (error) {
            logger.warn(`Failed to fetch quote for case ${id}: ${error.message}`);
        }

        // Get rate card and fee information (for manually created cases or cases with rate card)
        let billing: any = null;
        if (caseEntity.rateCardId) {
            try {
                // Get transaction type from case metadata
                const transactionType = caseEntity.conveyancingMetadata?.transactionType;
                // Get property value from the case's property if available
                const propertyValue = caseEntity.property?.purchasePrice
                    ? Number(caseEntity.property.purchasePrice)
                    : undefined;

                billing = await this.getRateCardFees(
                    caseEntity.rateCardId,
                    transactionType,
                    propertyValue
                );
            } catch (error) {
                logger.warn(`Failed to fetch rate card fees for case ${id}: ${error.message}`);
            }
        }

        // Combine all information
        const details = {
            ...caseEntity,
            assignments,
            notes,
            attachments,
            contacts,
            relatedCases,
            events,
            auditTrail,
            milestones,
            payments,
            quote,
            billing
        };

        return details;
    }

    /**
     * Finds a case by case number
     */
    async findCaseByCaseNumber(caseNumber: string, userId: string): Promise<Case> {
        const tenantId = this.tenantContext.getTenantId();
        const cacheKey = CACHE_KEYS.CASE_BY_NUMBER(tenantId, caseNumber, userId);
        let cachedCase: Case | undefined;
        try {
            cachedCase = await this.cacheService.get<Case>(cacheKey);
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][CaseByNumber] tenantId=${tenantId} userId=${userId} caseNumber=${caseNumber} cacheKey=${cacheKey} - Failed to get case from cache: ${err?.message}`,
                err
            );
        }
        if (cachedCase) {
            logger.debug(
                `[CACHE HIT] Case by Number: key=${cacheKey} | value: ${JSON.stringify(cachedCase)}`
            );
            return cachedCase;
        }

        const caseEntity = await this.caseRepository.findByCaseNumber(caseNumber);

        if (!caseEntity) {
            throw new NotFoundException(`Case with number ${caseNumber} not found`);
        }

        const ttl = this.configService.get<number>('cache.ttl.caseDetails');
        try {
            await this.cacheService.set(cacheKey, caseEntity, ttl);
            logger.debug(
                `[CACHE SET] Case by Number: key=${cacheKey} | value: ${JSON.stringify(caseEntity)}`
            );
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][CaseByNumber] tenantId=${tenantId} userId=${userId} caseNumber=${caseNumber} cacheKey=${cacheKey} - Failed to set case in cache: ${err?.message}`,
                err
            );
        }
        return caseEntity;
    }

    /**
     * Finds cases with filtering, pagination, and sorting
     * Supports time-based filtering with filterBy parameter
     */
    async findCases(filterDto: CaseFilterDto): Promise<PaginatedResponse<Case>> {
        const [cases, total] = await this.caseRepository.findWithFilters(filterDto);

        return this.paginationService.createPaginatedResponse(
            cases,
            total,
            filterDto.page || 1,
            filterDto.limit || 10
        );
    }

    /**
     * Search cases by multiple criteria
     * Supports searching by ID, case number, assigned person name, or client name
     */
    async searchCases(
        searchQuery: string,
        searchType: 'id' | 'caseNumber' | 'assignedTo' | 'clientName',
        limit: number = 10,
        userId: string
    ): Promise<Case[]> {
        if (!searchQuery || searchQuery.length < 2) {
            return [];
        }

        switch (searchType) {
            case 'id':
                if (!isUuid(searchQuery)) {
                    return [];
                }
                try {
                    const caseEntity = await this.findCaseById(searchQuery, userId);
                    return caseEntity ? [caseEntity] : [];
                } catch (error) {
                    logger.error(`Failed to find case by ID ${searchQuery}: ${error.message}`);
                    return [];
                }

            case 'caseNumber':
                try {
                    const caseEntity = await this.findCaseByCaseNumber(searchQuery, userId);
                    return caseEntity ? [caseEntity] : [];
                } catch (error) {
                    logger.error(
                        `Failed to find case by case number ${searchQuery}: ${error.message}`
                    );
                    return [];
                }

            case 'assignedTo':
                // Find cases assigned to a person with matching name
                return this.caseRepository.findByAssignedPersonName(searchQuery, limit);

            case 'clientName':
                // Find cases with client name matching the search query
                return this.caseRepository.findByClientName(searchQuery, limit);

            default:
                return [];
        }
    }

    /**
     * Updates a case
     */
    async updateCase(
        id: string,
        updateCaseDto: UpdateCaseDto,
        userId: string,
        userName: string,
        request: Request
    ): Promise<Case> {
        const caseEntity = await this.findCaseById(id, userId);

        // Verify client exists if clientId is provided
        if (updateCaseDto.clientId) {
            const client = await this.clientRepository.findOne({
                where: { id: updateCaseDto.clientId }
            });

            if (!client) {
                throw new BadRequestException(`Client with ID ${updateCaseDto.clientId} not found`);
            }
        }

        // Store old values for audit
        const oldValues = { ...caseEntity };

        // Check if status is changing
        const isStatusChanging = updateCaseDto.status && updateCaseDto.status !== caseEntity.status;
        const oldStatus = caseEntity.status;

        // Update case
        Object.assign(caseEntity, {
            ...updateCaseDto,
            deadline: updateCaseDto.deadline
                ? new Date(updateCaseDto.deadline)
                : caseEntity.deadline,
            updatedAt: new Date()
        });

        const updatedCase = await this.caseRepository.save(caseEntity);

        // Log status change separately if status changed
        if (isStatusChanging) {
            await this.caseAuditService.logStatusChange(
                updatedCase.id,
                userId,
                userName,
                request,
                oldStatus,
                updatedCase.status
            );

            // Send status change notification
            const client = await this.clientRepository.findOne({
                where: { id: updatedCase.clientId }
            });
            if (client && client.email) {
                await this.caseNotificationService.sendCaseStatusNotification(
                    { ...updatedCase, client },
                    oldStatus,
                    updatedCase.status,
                    userId,
                    [client.email]
                );
            }
        }

        // Log case update
        await this.caseAuditService.logCaseUpdate(
            updatedCase.id,
            userId,
            userName,
            request,
            oldValues,
            updatedCase
        );

        // Invalidate cache
        const tenantId = this.tenantContext.getTenantId();
        try {
            await this.cacheService.del(CACHE_KEYS.CASE_BY_ID(tenantId, id, userId));
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][CaseUpdate] tenantId=${tenantId} userId=${userId} caseId=${id} cacheKey=${CACHE_KEYS.CASE_BY_ID(tenantId, id, userId)} - Failed to delete case cache: ${err?.message}`,
                err
            );
        }
        try {
            await this.cacheService.del(
                CACHE_KEYS.CASE_BY_NUMBER(tenantId, updatedCase.caseNumber, userId)
            );
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][CaseUpdate] tenantId=${tenantId} userId=${userId} caseNumber=${updatedCase.caseNumber} cacheKey=${CACHE_KEYS.CASE_BY_NUMBER(tenantId, updatedCase.caseNumber, userId)} - Failed to delete case cache: ${err?.message}`,
                err
            );
        }

        return updatedCase;
    }

    /**
     * Checks if a case can be closed
     * @param caseId The case ID
     * @returns Object indicating if the case can be closed and the reason if not
     */
    async canCloseCaseCheck(
        caseId: string,
        userId: string
    ): Promise<{ canClose: boolean; reason?: string }> {
        const caseEntity = await this.findCaseById(caseId, userId);

        // Check if case is in a status that can be closed
        if (![CaseStatus.APPROVED, CaseStatus.IN_PROGRESS].includes(caseEntity.status)) {
            return {
                canClose: false,
                reason: `Case must be in APPROVED or IN_PROGRESS status to be closed (current: ${caseEntity.status})`
            };
        }

        // Check if case has at least one note
        const notes = await this.caseNoteService.getCaseNotes(caseId);
        if (notes.length === 0) {
            return { canClose: false, reason: 'Case must have at least one note before closing' };
        }

        // Check if case has at least one assignment
        const assignments = await this.caseAssignmentService.getCaseAssignments(caseId);
        if (assignments.length === 0) {
            return {
                canClose: false,
                reason: 'Case must be assigned to at least one user before closing'
            };
        }

        // All checks passed
        return { canClose: true };
    }

    /**
     * Gets the corresponding audit action for a status
     * @param status The case status
     * @returns The corresponding audit action or undefined
     */
    private getStatusAction(status: CaseStatus): CaseAuditAction | undefined {
        const statusActionMap: Record<CaseStatus, CaseAuditAction> = {
            [CaseStatus.DRAFT]: CaseAuditAction.STATUS_DRAFT,
            [CaseStatus.SUBMITTED]: CaseAuditAction.STATUS_SUBMITTED,
            [CaseStatus.UNDER_REVIEW]: CaseAuditAction.STATUS_CHANGED,
            [CaseStatus.ASSIGNED]: CaseAuditAction.STATUS_CHANGED,
            [CaseStatus.IN_PROGRESS]: CaseAuditAction.STATUS_IN_PROGRESS,
            [CaseStatus.ON_HOLD]: CaseAuditAction.STATUS_ON_HOLD,
            [CaseStatus.PENDING_APPROVAL]: CaseAuditAction.STATUS_CHANGED,
            [CaseStatus.APPROVED]: CaseAuditAction.STATUS_APPROVED,
            [CaseStatus.REJECTED]: CaseAuditAction.STATUS_DECLINED,
            [CaseStatus.RESOLVED]: CaseAuditAction.STATUS_CHANGED,
            [CaseStatus.CLOSED]: CaseAuditAction.STATUS_CLOSED,
            [CaseStatus.REOPENED]: CaseAuditAction.STATUS_CHANGED,
            [CaseStatus.ARCHIVED]: CaseAuditAction.STATUS_ARCHIVED
        };

        return statusActionMap[status];
    }

    /**
     * Audits access to a case
     * @param caseId The case ID
     * @param userId The user ID
     * @param userName The user name
     * @param request The HTTP request
     */
    async auditCaseAccess(
        caseId: string,
        userId: string,
        userName: string,
        request: Request
    ): Promise<void> {
        await this.caseAuditService.logAction(
            caseId,
            CaseAuditAction.ACCESSED,
            userId,
            userName,
            this.getIpAddress(request),
            {
                accessTime: new Date().toISOString(),
                accessType: 'details_view'
            }
        );
    }

    /**
     * Extracts the IP address from the request
     * @param request The HTTP request
     * @returns The IP address
     */
    private getIpAddress(request: Request): string {
        return (
            (request.headers['x-forwarded-for'] as string) ||
            request.socket.remoteAddress ||
            'unknown'
        );
    }

    /**
     * Deletes a case
     */
    async deleteCase(id: string, userId: string): Promise<void> {
        const caseEntity = await this.findCaseById(id, userId);
        await this.caseRepository.remove(caseEntity);

        // Invalidate cache
        const tenantId = this.tenantContext.getTenantId();
        try {
            await this.cacheService.del(CACHE_KEYS.CASE_BY_ID(tenantId, id, userId));
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][CaseDelete] tenantId=${tenantId} userId=${userId} caseId=${id} cacheKey=${CACHE_KEYS.CASE_BY_ID(tenantId, id, userId)} - Failed to delete case cache: ${err?.message}`,
                err
            );
        }
        try {
            await this.cacheService.del(
                CACHE_KEYS.CASE_BY_NUMBER(tenantId, caseEntity.caseNumber, userId)
            );
        } catch (err) {
            logger.warn(
                `[CACHE][ERROR][CaseDelete] tenantId=${tenantId} userId=${userId} caseNumber=${caseEntity.caseNumber} cacheKey=${CACHE_KEYS.CASE_BY_NUMBER(tenantId, caseEntity.caseNumber, userId)} - Failed to delete case cache: ${err?.message}`,
                err
            );
        }
    }

    /**
     * Using getKeys() for cache maintenance
     */
    async invalidateCaseCache(caseId: string, userId: string): Promise<void> {
        const tenantId = this.tenantContext.getTenantId();
        const caseEntity = await this.caseRepository.findOne({ where: { id: caseId } });
        const keysToDelete = [CACHE_KEYS.CASE_BY_ID(tenantId, caseId, userId)];
        if (caseEntity) {
            keysToDelete.push(CACHE_KEYS.CASE_BY_NUMBER(tenantId, caseEntity.caseNumber, userId));
        }
        if (keysToDelete.length > 0) {
            try {
                await this.cacheService.delMultiple(keysToDelete);
                logger.debug(`[CACHE INVALIDATE] Deleted keys: ${keysToDelete.join(', ')}`);
            } catch (err) {
                logger.warn(
                    `[CACHE][ERROR][CaseInvalidate] tenantId=${tenantId} userId=${userId} caseId=${caseId} cacheKeys=[${keysToDelete.join(', ')}] - Failed to delete multiple case cache keys: ${err?.message}`,
                    err
                );
            }
        }
    }

    /**
     * Using size() for monitoring
     */
    async getCacheStats(): Promise<{ totalEntries: number }> {
        const size = await this.cacheService.size();
        return { totalEntries: size };
    }

    /**
     * Get rate card fees for a given rate card ID, filtered by transaction type and property value
     */
    async getRateCardFees(
        rateCardId: string,
        transactionType?: string,
        propertyValue?: number
    ): Promise<any> {
        try {
            logger.info(
                `Getting rate card fees for: rateCardId=${rateCardId}, transactionType=${transactionType}, propertyValue=${propertyValue}`
            );

            // Get rate card details
            const rateCard = await this.rateCardRepository.findOne({
                where: { id: rateCardId }
            });

            if (!rateCard) {
                return null;
            }

            // Get all fee items for this rate card first
            let feeItems: RateCardFeeItem[] =
                await this.rateCardFeeItemRepository.findByRateCardId(rateCardId);
            logger.info(`Found ${feeItems.length} total fee items for rate card ${rateCardId}`);

            // Filter by transaction type if provided
            if (transactionType) {
                const originalCount = feeItems.length;
                feeItems = feeItems.filter((item) => {
                    if (!item.applicableFor) return false;

                    // Exact match for applicableFor field
                    const isApplicable = item.applicableFor === transactionType;

                    if (isApplicable) {
                        logger.info(
                            `Fee item ${item.label} (${item.applicableFor}) matches transaction type ${transactionType}`
                        );
                    }

                    return isApplicable;
                });
                logger.info(
                    `After transaction type filtering: ${feeItems.length} items (was ${originalCount})`
                );
            }

            // Filter by property value range if provided
            if (propertyValue && propertyValue > 0) {
                feeItems = feeItems.filter((item) => {
                    // If no range is specified, include the item
                    if (!item.rangeStart && !item.rangeEnd) {
                        return true;
                    }

                    const rangeStart = item.rangeStart ? Number(item.rangeStart) : 0;
                    const rangeEnd = item.rangeEnd
                        ? Number(item.rangeEnd)
                        : Number.MAX_SAFE_INTEGER;

                    return propertyValue >= rangeStart && propertyValue <= rangeEnd;
                });
            }

            // Group fees by category
            const feesByCategory = feeItems.reduce((acc, item) => {
                const category = item.categoryName || 'Other Fees';
                if (!acc[category]) {
                    acc[category] = [];
                }
                acc[category].push({
                    id: item.id,
                    label: item.label,
                    feeType: item.feeType,
                    netFee: item.netFee,
                    vatFee: item.vatFee,
                    totalFee: item.totalFee,
                    vatType: item.vatType,
                    applicableFor: item.applicableFor,
                    perParty: item.perParty,
                    dynamic: item.dynamic,
                    rangeStart: item.rangeStart,
                    rangeEnd: item.rangeEnd,
                    conditionSlug: item.conditionSlug,
                    displayOrder: item.displayOrder,
                    notes: item.notes,
                    active: item.active
                });
                return acc;
            }, {});

            // Calculate grand totals
            let grandTotalNet = 0;
            let grandTotalVat = 0;
            let grandTotal = 0;

            feeItems.forEach((item) => {
                const netFee = parseFloat(String(item.netFee || '0'));
                const vatFee = parseFloat(String(item.vatFee || '0'));
                const totalFee = parseFloat(String(item.totalFee || '0'));

                grandTotalNet += netFee;
                grandTotalVat += vatFee;
                grandTotal += totalFee;
            });

            // Round to 2 decimal places
            grandTotalNet = Math.round(grandTotalNet * 100) / 100;
            grandTotalVat = Math.round(grandTotalVat * 100) / 100;
            grandTotal = Math.round(grandTotal * 100) / 100;

            logger.info(
                `Calculated totals: Net=${grandTotalNet}, VAT=${grandTotalVat}, Total=${grandTotal}`
            );

            return {
                rateCard: {
                    id: rateCard.id,
                    providerName: rateCard.providerName,
                    displayName: rateCard.displayName,
                    description: rateCard.description,
                    version: rateCard.version,
                    effectiveDate: rateCard.effectiveDate
                },
                feesByCategory,
                grandTotalNet,
                grandTotalVat,
                grandTotal,
                totalFeeItems: feeItems.length,
                appliedFeeItems: feeItems.length,
                categories: Object.keys(feesByCategory)
            };
        } catch (error) {
            logger.error(`Error fetching rate card fees for ${rateCardId}:`, error);
            return null;
        }
    }

    /**
     * Enhance case response with rate card fee information
     */
    async enhanceCaseWithRateCardFees(caseData: Case, transactionType?: string): Promise<any> {
        const enhancedCase: any = { ...caseData };

        // Add rate card fees if rate card is present
        if (caseData.rateCardId) {
            // Get property value from the case's property if available
            const propertyValue = caseData.property?.purchasePrice
                ? Number(caseData.property.purchasePrice)
                : undefined;

            const rateCardFees = await this.getRateCardFees(
                caseData.rateCardId,
                transactionType,
                propertyValue
            );
            if (rateCardFees) {
                enhancedCase.billing = rateCardFees;
            }
        }

        return enhancedCase;
    }
}
