import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Case } from '@app/common/typeorm/entities/tenant/case.entity';
import { CasePayment } from '@app/common/typeorm/entities/tenant/case-payment.entity';
import { CaseService } from './services/case.service';
import { CaseRepository } from './repositories/case.repository';
import { ClientRepository } from './repositories/client.repository';
import { PropertyRepository } from './repositories/property.repository';
import { RateCardRepository } from 'apps/quote-engine/src/repositories/rate-card.repository';
import { RateCardFeeItemRepository } from 'apps/quote-engine/src/repositories/rate-card-fee-item.repository';
import { CaseNumberGenerator } from './utils/case-number-generator';
import { PaginationService } from './services/pagination.service';
import { CaseAuditService } from './services/case-audit.service';
import { CaseNoteService } from './services/case-note.service';
import { CaseAttachmentService } from './services/case-attachment.service';
import { CaseAssignmentService } from './services/case-assignment.service';
import { CaseContactService } from './services/case-contact.service';
import { CaseEventService } from './services/case-event.service';
import { CaseRelationService } from './services/case-relation.service';
import { CaseNotificationService } from './services/case-notification.service';
import { CasePaymentService } from './services/case-payment.service';
import { CaseAuditRepository } from './repositories/case-audit.repository';
import { CaseNoteRepository } from './repositories/case-note.repository';
import { CaseAttachmentRepository } from './repositories/case-attachment.repository';
import { CaseAssignmentRepository } from './repositories/case-assignment.repository';
import { CaseContactRepository } from './repositories/case-contact.repository';
import { CaseEventRepository } from './repositories/case-event.repository';
import { CaseRelationRepository } from './repositories/case-relation.repository';
import { CasePaymentRepository } from './repositories/case-payment.repository';
import { TaskManagementModule } from 'apps/task-management/src/task-management.module';
import { CommonModule } from '@app/common';
import { RedisModule } from '@app/common/cache/redis.module';
import { ConveyancingMilestoneService } from './services/conveyancing-milestone.service';
import { AuthService } from 'apps/auth/src/services/auth.service';
import { KeycloakService } from 'apps/auth/src/services/keycloak.service';
import { KeycloakHttpService } from 'apps/auth/src/services/keycloak-http.service';
import { AuthNotificationService } from 'apps/auth/src/services/auth-notification.service';

/**
 * Module that provides the CaseService and its dependencies
 * This module can be imported by other modules that need to use the CaseService
 */
@Module({
    imports: [
        TypeOrmModule.forFeature([Case, CasePayment]),
        RedisModule,
        forwardRef(() => TaskManagementModule),
        CommonModule // Import the TaskManagementModule with forwardRef to handle circular dependency
    ],
    providers: [
        CaseService,
        CaseRepository,
        ClientRepository,
        PropertyRepository,
        RateCardRepository,
        RateCardFeeItemRepository,
        CaseNumberGenerator,
        PaginationService,
        CaseAuditService,
        CaseNoteService,
        CaseAttachmentService,
        CaseAssignmentService,
        CaseContactService,
        CaseEventService,
        CaseRelationService,
        CaseNotificationService,
        CasePaymentService,
        CaseAuditRepository,
        CaseNoteRepository,
        CaseAttachmentRepository,
        CaseAssignmentRepository,
        CaseContactRepository,
        CaseEventRepository,
        CaseRelationRepository,
        CasePaymentRepository,
        CaseNotificationService,
        CasePaymentService,
        ConveyancingMilestoneService,
        AuthService,
        KeycloakService,
        KeycloakHttpService,
        AuthNotificationService
    ],
    exports: [
        CaseService,
        PaginationService,
        CaseAssignmentService,
        ConveyancingMilestoneService,
        CaseAuditService
    ]
})
export class CaseServiceModule {}
