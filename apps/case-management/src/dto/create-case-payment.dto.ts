import {
    IsDate<PERSON><PERSON>,
    IsEnum,
    IsNotEmpty,
    IsN<PERSON>ber,
    IsOptional,
    IsString,
    Min
} from 'class-validator';
import { PaymentMethod } from '@app/common/typeorm/entities';

export class CreateCasePaymentDto {
    @IsDateString()
    @IsNotEmpty()
    paymentDate: string;

    @IsString()
    @IsNotEmpty()
    referenceNumber: string;

    @IsEnum(PaymentMethod)
    @IsNotEmpty()
    method: PaymentMethod;

    @IsString()
    @IsOptional()
    description?: string;

    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0.01)
    @IsNotEmpty()
    amount: number;

    // Optional billing fields that can be updated on the case

    // Maps to case.depositAmount
    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @IsOptional()
    initialPayment?: number;

    // Maps to case.conveyancingMetadata.searchFees
    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @IsOptional()
    searchFees?: number;

    // Maps to case.conveyancingMetadata.balanceDue
    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @IsOptional()
    balanceDue?: number;

    // Maps to case.deadline
    @IsDateString()
    @IsOptional()
    dueDate?: string;

    // Maps to case.mortgageAmount
    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @IsOptional()
    mortgageAmount?: number;

    // Maps to case.conveyancingMetadata.sdtlDue
    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @IsOptional()
    sdtlDue?: number;

    // Maps to case.conveyancingMetadata.landRegistryFee
    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @IsOptional()
    landRegistryFee?: number;
}
