import {
    IsDateS<PERSON>,
    Is<PERSON>num,
    IsNotEmpty,
    IsN<PERSON>ber,
    IsOptional,
    IsString,
    Min
} from 'class-validator';
import { PaymentMethod } from '@app/common/typeorm/entities';

export class CreateCasePaymentDto {
    @IsDateString()
    @IsNotEmpty()
    paymentDate: string;

    @IsString()
    @IsNotEmpty()
    referenceNumber: string;

    @IsEnum(PaymentMethod)
    @IsNotEmpty()
    method: PaymentMethod;

    @IsString()
    @IsOptional()
    description?: string;

    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0.01)
    @IsNotEmpty()
    amount: number;

    // Optional billing fields that can be updated on the case
    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @IsOptional()
    initialPayment?: number;

    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @IsOptional()
    searchFees?: number;

    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @IsOptional()
    balanceDue?: number;

    @IsDateString()
    @IsOptional()
    dueDate?: string;

    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @IsOptional()
    mortgageAmount?: number;

    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @IsOptional()
    sdtlDue?: number;

    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @IsOptional()
    landRegistryFee?: number;
}
