import {
    Injectable,
    CanActivate,
    ExecutionContext,
    UnauthorizedException,
    Logger
} from '@nestjs/common';
import { KeycloakService } from 'apps/auth/src/services/keycloak.service';
import { Request } from 'express';
import { TenantRoleRepository } from '@app/common/repositories/tenant-role.repository';
import { TenantRepository } from '@app/common/repositories/tenant.repository';
import { UserRepository } from '@app/common/repositories/user.repository';
import { ROLE_GROUPS } from '@app/common/permissions/role-group-permissions.defaults';
import { TenantContextService } from '@app/common/multi-tenancy/tenant-context.service';
import * as jwt from 'jsonwebtoken';
import * as crypto from 'crypto';

/**
 * Guard that validates JWT tokens using Keycloak's public key
 */
@Injectable()
export class JwtGuard implements CanActivate {
    private readonly logger = new Logger(JwtGuard.name);

    constructor(
        private readonly keycloakService: KeycloakService,
        private readonly tenantRoleRepository: TenantRoleRepository,
        private readonly tenantRepository: TenantRepository,
        private readonly userRepository: UserRepository,
        private readonly tenantContextService: TenantContextService
    ) {}

    /**
     * Validates the JWT token in the request
     */
    async canActivate(context: ExecutionContext): Promise<boolean> {
        const request = context.switchToHttp().getRequest<Request>();
        const token = this.extractTokenFromRequest(request);
        const realm = this.extractRealmFromRequest(request);

        if (!token) {
            throw new UnauthorizedException('No token provided');
        }

        if (!realm) {
            throw new UnauthorizedException('No realm provided');
        }

        try {
            // Verify the token using the public key from JWKS
            const decodedToken = await this.verifyToken(token, realm);
            // Log the raw realm and resource access roles for debugging
            if (decodedToken.realm_access) {
                this.logger.debug(
                    `Raw realm_access roles: ${JSON.stringify(decodedToken.realm_access.roles || [])}`
                );
            }
            if (decodedToken.resource_access && typeof decodedToken.resource_access === 'object') {
                Object.keys(decodedToken.resource_access).forEach((client) => {
                    const clientRoles = decodedToken.resource_access?.[client]?.roles || [];
                    this.logger.debug(
                        `Raw resource_access for client ${client}: ${JSON.stringify(clientRoles)}`
                    );
                });
            }

            const roles = this.extractRoles(decodedToken);
            this.logger.debug(`JWT Guard extracted roles: ${JSON.stringify(roles)}`);

            decodedToken.roles = roles;

            // Get user email from token
            const userEmail = decodedToken.email;
            if (!userEmail) {
                throw new UnauthorizedException('Invalid token: no email found');
            }

            // --- ENHANCEMENT: Attach all role group info ---
            const roleGroups: { key: string; isAdmin: boolean; roleId: string }[] = [];
            let isSuperAdmin = false;
            let primaryRoleGroupKey: string | null = null;
            let primaryRoleGroupId: string | null = null;
            let isGroupAdmin = false;
            let user: any = null;

            const headerTenantId = request.headers['x-tenant-id'];
            const cookieTenantId = request.cookies?.selected_tenant_id;
            const tokenTenantId = decodedToken.tenantId;
            const tenantId = headerTenantId || cookieTenantId || tokenTenantId;

            this.logger.debug(
                `JWT Guard - Header tenant ID: ${headerTenantId}, Cookie tenant ID: ${cookieTenantId}, Token tenant ID: ${tokenTenantId}, Selected: ${tenantId}`
            );
            if (tenantId) {
                // Set tenant context before making tenant-specific database operations
                await this.setTenantContext(tenantId as string, realm);

                // Get user from tenant database
                user = await this.userRepository.findOne({ where: { email: userEmail } });
                if (user) {
                    const tenantRoles = await this.tenantRoleRepository.getUserRoles(user.id);
                    this.logger.debug(
                        `JWT Guard found tenant roles:`,
                        tenantRoles.map((r) => ({ name: r.name, id: r.id }))
                    );
                    for (const role of tenantRoles) {
                        // Match against all role group keys
                        for (const group of ROLE_GROUPS) {
                            if (role.name === `${group.key}_user`) {
                                roleGroups.push({
                                    key: group.key,
                                    isAdmin: false,
                                    roleId: role.id
                                });
                                this.logger.debug(
                                    `JWT Guard matched user role: ${role.name} -> ${group.key}`
                                );
                            }
                            if (role.name === `${group.key}_admin`) {
                                roleGroups.push({ key: group.key, isAdmin: true, roleId: role.id });
                                this.logger.debug(
                                    `JWT Guard matched admin role: ${role.name} -> ${group.key}`
                                );
                            }
                        }
                    }
                    // Set primary group as the first found
                    if (roleGroups.length > 0) {
                        primaryRoleGroupKey = roleGroups[0].key ?? null;
                        primaryRoleGroupId = roleGroups[0].roleId ?? null;
                        isGroupAdmin = roleGroups[0].isAdmin;
                        this.logger.debug(
                            `JWT Guard set primary role group: ${primaryRoleGroupKey}, isAdmin: ${isGroupAdmin}`
                        );
                    } else {
                        this.logger.debug(`JWT Guard found no matching role groups for user`);
                    }
                }
                isSuperAdmin = roles.includes('SUPER_ADMIN');
            }
            request['user'] = {
                systemUserId: user?.id,
                ...decodedToken,
                roleGroups, // all group assignments
                roleGroupKey: primaryRoleGroupKey, // first group
                roleGroupId: primaryRoleGroupId, // first group id
                isGroupAdmin,
                isSuperAdmin
            };

            // Log the user object with roles to ensure they're properly attached
            this.logger.debug(
                `JWT Guard attached user: ${decodedToken.preferred_username || decodedToken.sub}`
            );
            this.logger.debug(`JWT Guard attached roles: ${JSON.stringify(decodedToken.roles)}`);

            return true;
        } catch (error) {
            this.logger.error(`Token validation failed: ${error.message}`, error.stack);
            throw new UnauthorizedException('Invalid token');
        }
    }

    /**
     * Extracts the token from the request
     * Checks Authorization header first, then cookies
     */
    private extractTokenFromRequest(request: Request): string | null {
        // Check Authorization header
        const authHeader = request.headers.authorization;
        if (authHeader && authHeader.startsWith('Bearer ')) {
            return authHeader.substring(7);
        }

        // Check cookies
        if (request.cookies && request.cookies.access_token) {
            return request.cookies.access_token;
        }

        return null;
    }

    /**
     * Extracts the realm from the request
     * Checks custom header first, then cookies, then query params
     */
    private extractRealmFromRequest(request: Request): string | null {
        // Check custom header first
        const realmHeader = request.headers['x-realm'] as string;
        this.logger.debug(`Extracted realm from x-realm header: ${realmHeader}`);
        if (realmHeader) {
            return realmHeader;
        }

        // Fallback to cookies if header not present
        if (request.cookies && request.cookies.realm) {
            this.logger.debug(`Extracted realm from realm cookie: ${request.cookies.realm}`);
            return request.cookies.realm;
        }

        return null;
    }

    /**
     * Sets the tenant context for database operations
     * This is crucial before making any tenant-specific database queries
     */
    private async setTenantContext(_tenantId: string, realm: string): Promise<void> {
        try {
            // Get tenant information from tenant repository
            const tenant = await this.tenantRepository.findByRealm(realm);
            if (tenant) {
                // Set the tenant context for subsequent database operations
                this.tenantContextService.setTenant(tenant.id, {
                    id: tenant.id,
                    realm: tenant.realm,
                    displayName: tenant.displayName || `${tenant.adminFirstName}-${tenant.realm}`,
                    enabled: tenant.enabled
                });
                this.logger.debug(`Tenant context set for tenant: ${tenant.id} (realm: ${realm})`);
            } else {
                this.logger.warn(`Tenant not found for realm: ${realm}`);
            }
        } catch (error) {
            this.logger.error(`Failed to set tenant context for realm ${realm}:`, error);
            // Don't throw here as we want to continue with authentication
            // The tenant-specific operations will fail gracefully if context is not set
        }
    }

    /**
     * Verify JWT token using Keycloak public key
     */
    private async verifyToken(token: string, realm: string): Promise<any> {
        try {
            // Get the JWKS
            const jwks = await this.keycloakService.getJwks(realm);

            // Decode the token header to get the key ID
            const decodedHeader = jwt.decode(token, { complete: true })?.header as
                | { kid?: string }
                | undefined;

            if (!decodedHeader || !decodedHeader.kid) {
                throw new Error('Invalid token header');
            }

            // Find the matching key
            const key = jwks.keys.find((k) => k.kid === decodedHeader.kid);

            if (!key) {
                throw new Error('Key not found in JWKS');
            }

            // Convert the JWK to a PEM certificate
            const publicKey = this.jwkToPem(key as any);

            // Verify the token
            return jwt.verify(token, publicKey) as any;
        } catch (error) {
            this.logger.error(`Token verification failed: ${error.message}`);
            throw new UnauthorizedException('Invalid token');
        }
    }

    /**
     * Convert JWK to PEM format
     */
    private jwkToPem(jwk: any): string {
        // For RSA keys
        if (jwk.kty === 'RSA') {
            // Convert base64url to base64
            const modulus = this.base64UrlToBase64(jwk.n);
            const exponent = this.base64UrlToBase64(jwk.e);

            // Create a Buffer from the base64 strings
            const modulusBuffer = Buffer.from(modulus, 'base64');
            const exponentBuffer = Buffer.from(exponent, 'base64');

            // Create a node.js key object
            const key = crypto.createPublicKey({
                key: {
                    kty: 'RSA',
                    n: modulusBuffer.toString('base64'),
                    e: exponentBuffer.toString('base64')
                },
                format: 'jwk'
            });

            // Export as PEM
            return key.export({ type: 'spki', format: 'pem' }).toString();
        }

        throw new Error(`Unsupported key type: ${jwk.kty}`);
    }

    /**
     * Converts base64url to base64
     */
    private base64UrlToBase64(base64Url: string): string {
        let base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');

        // Add padding if needed
        while (base64.length % 4) {
            base64 += '=';
        }

        return base64;
    }

    /**
     * Extract roles from decoded token
     */
    private extractRoles(decodedToken: any): string[] {
        const roles: string[] = [];

        // Extract realm roles
        if (decodedToken.realm_access?.roles) {
            roles.push(...decodedToken.realm_access.roles);
        }

        // Extract client roles
        if (decodedToken.resource_access) {
            Object.keys(decodedToken.resource_access).forEach((client) => {
                const clientRoles = decodedToken.resource_access[client]?.roles || [];
                roles.push(...clientRoles);
            });
        }

        // Handle realm-management client roles for super admin privileges
        if (decodedToken.resource_access?.['realm-management']?.roles) {
            const managementRoles = decodedToken.resource_access['realm-management'].roles;

            // If user has realm-admin role, give them SUPER_ADMIN privileges
            if (managementRoles.includes('realm-admin')) {
                roles.push('SUPER_ADMIN');
                this.logger.debug('Added SUPER_ADMIN role for realm-admin client role');
            }

            // If user has manage-users or manage-realm role, give them ADMIN privileges
            if (
                managementRoles.includes('manage-users') ||
                managementRoles.includes('manage-realm')
            ) {
                if (!roles.includes('ADMIN')) {
                    roles.push('ADMIN');
                    this.logger.debug('Added ADMIN role for realm management client role');
                }
            }
        }

        return [...new Set(roles)]; // Remove duplicates
    }
}
